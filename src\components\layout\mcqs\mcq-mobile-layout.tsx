import React, { Suspense, useState, useMemo } from "react";
import { useRouter } from "@tanstack/react-router";
import { routeList } from "@/lib/route-list";
import { ChevronLeft, MoreHorizontal } from "react-feather";
import { But<PERSON> } from "@/components/ui/button";
import {
	Sheet,
	SheetContent,
	SheetDescription,
	SheetTitle,
	SheetTrigger,
} from "@/components/ui/sheet";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { subjects } from "@/features/learning/constants";
import { Test } from "@/features/mcqs/types";
import { MCQPlaceholder } from "@/components/layout/mcqs/mcq-placeholder";
import MobileTestOptions from "@/components/layout/mcqs/mobile-test-option";
import MCQSolver from "@/components/layout/mcqs/mcq-solver";
import { Timer } from "@/components/ui/timer";
import { useToast } from "@/hooks/use-toast";

const MCQCard = React.lazy(() => import("./mcq-card"));

type MCQMobileLayoutProps = {
	test: Test;
	liveCheckEnabled: boolean;
	setLiveCheckEnabled: (enabled: boolean) => void;
	selectedAnswers: Record<string, number>;
	answerStatus: Record<string, boolean>;
	showResults: boolean;
	testTimesUp: boolean;
	onAnswerSelect: (questionId: string, optionIndex: number) => void;
	onCheckAnswers: () => void;
	setShowResults: (enabled: boolean) => void;
};

// Define a type for the subject IDs based on the subjects constant
type SubjectId = (typeof subjects)[number]["id"];

const MCQMobileLayout = ({
	test,
	liveCheckEnabled,
	setLiveCheckEnabled,
	selectedAnswers,
	answerStatus,
	showResults,
	onAnswerSelect,
	onCheckAnswers,
	setShowResults,
	testTimesUp,
}: MCQMobileLayoutProps) => {
	// Get all subjects that have MCQs in the test, sorted by tab order
	const availableSubjects = useMemo(() => {
		// Get unique subjects from MCQs
		const uniqueSubjects = [...new Set(test.mcqs.map((mcq) => mcq.subject))];

		// Sort according to the order in the global subjects constant
		return subjects
			.filter((subject) => uniqueSubjects.includes(subject.id))
			.map((subject) => subject.id);
	}, [test.mcqs]);

	const defaultSubject =
		availableSubjects.length > 0 ? availableSubjects[0] : subjects[0].id;

	// State to track the currently selected subject
	const [activeSubject, setActiveSubject] = useState<SubjectId>(
		defaultSubject as SubjectId
	);
	const { toast } = useToast();

	// Parse test duration to get minutes and seconds for the timer
	const parseTestDuration = () => {
		try {
			// Split duration into hours, minutes, seconds
			const parts = test.duration.split(":");
			// Extract hours, minutes, seconds - use nullish coalescing to ensure we have valid numbers
			const hours = parseInt(parts[0] ?? "0", 10);
			const minutes = parseInt(parts[1] ?? "0", 10);
			const seconds = parseInt(parts[2] ?? "0", 10);

			// Calculate total minutes for the timer
			return {
				minutes: hours * 60 + minutes,
				seconds: seconds,
			};
		} catch (error) {
			console.error("Error parsing test duration:", error);
			return { minutes: 30, seconds: 0 }; // Default to 30 minutes if parsing fails
		}
	};

	const { minutes, seconds } = parseTestDuration();

	const handleTimeEnd = () => {
		toast({
			title: "Time's up!",
			description: "Your test time has ended. Please submit your answers.",
			variant: "destructive",
		});
	};

	// Filter MCQs based on selected subject
	const filteredMCQs = test.mcqs.filter((mcq) => mcq.subject === activeSubject);

	// Group MCQs by subject based on the tab order
	const mcqsBySubject = useMemo(() => {
		const grouped: Record<string, typeof test.mcqs> = {};

		// Group MCQs by subject in the tab display order
		availableSubjects.forEach((subject) => {
			grouped[subject] = test.mcqs.filter((mcq) => mcq.subject === subject);
		});

		return grouped;
	}, [test.mcqs, availableSubjects]);

	// Create a mapping of question IDs to their sequential numbers based on tab order
	const questionNumberMap = useMemo(() => {
		const map = new Map<string, number>();
		let counter = 1;

		// For each subject in the tab order
		availableSubjects.forEach((subject) => {
			// Get questions for this subject
			const subjectMCQs = mcqsBySubject[subject] || [];

			// Assign sequential numbers
			subjectMCQs.forEach((mcq) => {
				map.set(mcq.id, counter++);
			});
		});

		return map;
	}, [mcqsBySubject, availableSubjects]);

	const { history } = useRouter();
	const pathname = window.location.pathname;
	const currentRoute = routeList
		.find((menu) => menu.menus.some((m) => m.href === pathname))
		?.menus.find((m) => m.href === pathname);

	const label = currentRoute?.label;

	const handleClick = () => {
		history.go(-1);
	};

	const handleTabClick = (subjectId: string) => {
		setActiveSubject(subjectId as SubjectId);
	};

	return (
		<div className="flex flex-col h-screen bg-gray-50">
			<div className="bg-accent text-white p-4">
				<div className="flex items-center justify-between my-4 py-4">
					<div className="flex items-center text-center text gap-2">
						<ChevronLeft className="w-6 h-6" onClick={handleClick} />
					</div>
					<h2 className="font-medium">{test.title ?? label}</h2>
					<div className="flex items-center gap-2 text-sm bg-white text-accent py-1.5 px-2 rounded-full">
						<Timer
							initialMinutes={minutes}
							initialSeconds={seconds}
							onTimeEnd={handleTimeEnd}
							className="text-accent"
						/>
					</div>

					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant="ghost" size="icon" className="text-white">
								<MoreHorizontal className="w-5 h-5" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-60 p-3">
							<MobileTestOptions
								liveCheckEnabled={liveCheckEnabled}
								setLiveCheckEnabled={setLiveCheckEnabled}
							/>
						</DropdownMenuContent>
					</DropdownMenu>
				</div>

				<div className="overflow-x-auto pb-2">
					<div className="flex gap-2">
						{subjects
							.filter((subject) => availableSubjects.includes(subject.id))
							.map((subject) => (
								<Button
									key={subject.id}
									onClick={() => handleTabClick(subject.id)}
									className={`whitespace-nowrap rounded-full px-4 py-2 text-sm font-medium ${
										activeSubject === subject.id
											? "bg-white text-accent"
											: "bg-accent/20 text-white"
									}`}
								>
									{subject.label}
								</Button>
							))}
					</div>
				</div>
			</div>

			<div className="flex-1 overflow-y-auto px-4 py-2">
				{filteredMCQs.length > 0 ? (
					filteredMCQs.map((mcq) => (
						<div key={mcq.id} className="mcq-container">
							<Suspense fallback={<MCQPlaceholder />}>
								<MCQCard
									mcq={mcq}
									selectedAnswer={selectedAnswers[mcq.id]}
									isCorrect={answerStatus[mcq.id]}
									liveCheckEnabled={liveCheckEnabled}
									showResults={showResults}
									questionNumber={questionNumberMap.get(mcq.id)} // Use properly ordered question number
								/>
							</Suspense>
						</div>
					))
				) : (
					<div className="p-6 text-center">
						<p className="text-gray-500">
							No questions available for this subject.
						</p>
					</div>
				)}
			</div>

			<div className="p-4 border-t bg-white">
				{showResults ? (
					<div className="flex flex-col gap-3">
						<Button
							className="w-full bg-accent text-white rounded-full py-6"
							onClick={() => onCheckAnswers()}
						>
							Continue to iterate?
						</Button>
					</div>
				) : (
					<Sheet>
						<SheetTrigger asChild>
							<Button className="w-full bg-accent text-white rounded-full py-6">
								Choose Answer
							</Button>
						</SheetTrigger>
						<SheetContent side="bottom" className="p-0 max-h-[100vh]">
							<SheetTitle className="sr-only">Answer Selection</SheetTitle>
							<SheetDescription className="sr-only">
								Select your answer from the options below.
							</SheetDescription>
							<MCQSolver
								quizId={test.id}
								mcqs={test.mcqs}
								onAnswerSelect={onAnswerSelect}
								selectedAnswers={selectedAnswers}
								answerStatus={answerStatus}
								liveCheckEnabled={liveCheckEnabled}
								onCheckAnswers={onCheckAnswers}
								questionNumberMap={questionNumberMap}
								setShowResults={setShowResults}
								testTimesUp={testTimesUp}
								showResults={showResults}
							/>
						</SheetContent>
					</Sheet>
				)}
			</div>
		</div>
	);
};

export default MCQMobileLayout;
