import {
	useState,
	useRef,
	useImperativeHandle,
	forwardRef,
	useEffect,
} from "react";
import { ChevronDown, ChevronUp, Check } from "react-feather";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { MCQ } from "@/features/mcqs/types";
import { Button } from "@/components/ui/button";
import { Link } from 'react-scroll';
import MCQResult from "./mcq-result";
import { submitQuiz } from "@/features/tests/services";
import { toast } from "@/hooks/use-toast";
import { SubmitQuizPayload } from "@/features/tests/types";
import { updateAnalytics } from "@/features/user/services";


export type MCQSolverHandle = {
	scrollToQuestion: (questionNumber: number) => void;
};

type MCQSolverProps = {
	quizId: string;
	mcqs: MCQ[];
	onAnswerSelect: (questionId: string, optionIndex: number) => void;
	selectedAnswers: Record<string, number>;
	answerStatus: Record<string, boolean>;
	liveCheckEnabled: boolean;
	onCheckAnswers: () => void;
	questionNumberMap: Map<string, number>; // Add prop for question number mapping
	testTimesUp: boolean;
	showResults: boolean;
	setShowResults: (enabled: boolean) => void;

};

const MCQSolver = forwardRef<MCQSolverHandle, MCQSolverProps>(
	(
		{	
			quizId,
			mcqs,
			onAnswerSelect,
			selectedAnswers,
			answerStatus,
			liveCheckEnabled,
			onCheckAnswers,
			questionNumberMap, // Destructure the new prop
			testTimesUp,
			showResults,
			setShowResults,
		},
		ref
	) => {
		const [startTime] = useState(new Date());
		const [isOpen, setIsOpen] = useState(true);
		const [isLoading,setLoading] = useState(false)
		const scrollContainerRef = useRef<HTMLDivElement>(null);
		const questionRowsRef = useRef<Map<number, HTMLDivElement>>(new Map());
		const [highlightedQuestion, setHighlightedQuestion] = useState<
			number | null
		>(null);
		const attemptedQuestions = new Set(Object.keys(selectedAnswers));
		const totalQuestions = mcqs.length;
		const attemptedCount = attemptedQuestions.size;
		const allQuestionsAnswered = attemptedCount === totalQuestions;

		// Clear highlight effect after a delay
		useEffect(() => {
			if (highlightedQuestion !== null) {
				const timer = setTimeout(() => {
					setHighlightedQuestion(null);
				}, 2000);
				return () => clearTimeout(timer);
			}
			return undefined; // Add explicit return for when highlightedQuestion is null
		}, [highlightedQuestion]);


		useEffect(() => {
			if(testTimesUp){
				setShowResults(true)
			}
		}, [testTimesUp]);

		// Expose the scrollToQuestion method to parent components
		useImperativeHandle(ref, () => ({
			scrollToQuestion: (questionNumber: number) => {
				// Make sure the solver is open
				setIsOpen(true);
				// Set the highlighted question
				setHighlightedQuestion(questionNumber);
				// Find the row element for the question number
				const rowElement = questionRowsRef.current.get(questionNumber);
				// Use a longer timeout to ensure the collapsible content is fully visible
				// First timeout makes sure the collapsible is fully open
				setTimeout(() => {
					if (rowElement && scrollContainerRef.current) {
						// Calculate the scroll position - center the element in the viewport if possible
						const containerHeight = scrollContainerRef.current.clientHeight;
						const elementHeight = rowElement.clientHeight;
						const targetY =
							rowElement.offsetTop - containerHeight / 2 + elementHeight / 2;
						scrollContainerRef.current.scrollTo({
							top: Math.max(0, targetY),
							behavior: "smooth",
						});
						// Apply a second scroll after a delay to ensure it works
						setTimeout(() => {
							if (scrollContainerRef.current) {
								scrollContainerRef.current.scrollTo({
									top: Math.max(0, targetY),
									behavior: "smooth",
								});
							}
						}, 100);
					}
				}, 300); // Increased from 150ms to 300ms for more reliable rendering
			},
		}));

		const handleSelect = (questionId: string, optionIndex: number) => {
			if (
				!liveCheckEnabled &&
				Object.keys(answerStatus).length > 0 &&
				selectedAnswers[questionId] !== undefined
			) {
				return;
			}
			if (liveCheckEnabled && attemptedQuestions.has(questionId)) {
				return;
			}
			onAnswerSelect(questionId, optionIndex);
		};

		const getButtonClass = (questionId: string, optionIndex: number) => {
			// Get the current MCQ to check its correct answer
			const mcq = mcqs.find((m) => m.id === questionId);
			if (!mcq) return "bg-gray-100 hover:bg-gray-200";

			// If question hasn't been attempted yet
			if (!attemptedQuestions.has(questionId)) {
				return selectedAnswers[questionId] === optionIndex
					? "bg-blue-500 text-white"
					: "bg-gray-100 hover:bg-gray-200";
			}

			// If not in live check mode and results aren't shown yet
			if (!liveCheckEnabled && !Object.keys(answerStatus).length) {
				return selectedAnswers[questionId] === optionIndex
					? "bg-blue-500 text-white"
					: "bg-gray-100 hover:bg-gray-200";
			}

			// If this is the selected answer
			if (selectedAnswers[questionId] === optionIndex) {
				// Directly compare with mcq.correctAnswer to determine correctness
				const isSelectedCorrect = optionIndex === mcq.correctAnswer;
				return isSelectedCorrect
					? "bg-green-500 text-white"
					: "bg-red-500 text-white";
			}

			// If this is the correct answer (to show after checking)
			if (
				optionIndex === mcq.correctAnswer &&
				Object.keys(answerStatus).length > 0
			) {
				return "bg-green-500 text-white";
			}

			return "bg-gray-100 hover:bg-gray-200";
		};

		// Sort MCQs based on their global question number
		const sortedMCQs = [...mcqs].sort((a, b) => {
			const numA = questionNumberMap.get(a.id) || 0;
			const numB = questionNumberMap.get(b.id) || 0;
			return numA - numB;
		});

		const isSubmitTestDisabled = () => {
			if(testTimesUp) return false
			if(allQuestionsAnswered) return false
			if(Object.keys(answerStatus).length > 0) return true
			return true
		}

		const showResultsCard = (): boolean => {
			if(testTimesUp) return true
			if(liveCheckEnabled === false && allQuestionsAnswered && showResults) return true
			if(liveCheckEnabled && allQuestionsAnswered) return true
			return false
		}

		function getTimeTakenInSeconds(): number {
			const end = new Date();
			
			const diffMs = end.getTime() - startTime.getTime(); // in milliseconds
			return Math.floor(diffMs / 1000); // convert to seconds
		}
		
		const handleSubmitQuiz = async () => {
			try {
				setLoading(true)
				const payload: SubmitQuizPayload = {
					quizId,
					timeTaken: getTimeTakenInSeconds(),
					chosenOptions: Object.entries(selectedAnswers ?? {}).map(
					([mcqId, chosenOption]) => ({
						mcqId,
						chosenOption: chosenOption ?? 0,
					})
					)
				}
				const response = await submitQuiz(payload);
				console.log("response",response)
	
				if (response.data && response.data.success) {
					await updateAnalytics()
					onCheckAnswers()
				} else {
					throw new Error("Failed to submit quiz, something went wrong");
				}
			} catch (error) {
				console.error("Failed to submit quiz:", error);
				toast({
					title: "Error",
					description: "Failed to submit quiz. Please try again.",
					variant: "destructive",
				});
			} finally {
				setLoading(false);
			}
		};

		return (
			<Collapsible
				open={isOpen}
				onOpenChange={setIsOpen}
				className="lg:w-64 border-2 bg-white rounded-3xl transition-all duration-300 ease-in-out"
			>
				<div className="p-4 border-b flex items-center justify-between">
					<h3 className="text-lg font-bold text-gray-600">MCQ's Solver</h3>
					<CollapsibleTrigger asChild>
						<Button
							variant="icon"
							className="text-gray-500 hover:text-gray-700 transition-all duration-300 [&_svg]:size-6"
						>
							{isOpen ? (
								<ChevronUp className="w-5 h-5 transition-transform duration-300" />
							) : (
								<ChevronDown className="w-5 h-5 transition-transform duration-300" />
							)}
						</Button>
					</CollapsibleTrigger>
				</div>
				<CollapsibleContent className="transition-all duration-300 ease-in-out data-[state=closed]:animate-collapse-up data-[state=open]:animate-collapse-down">
					<div className="p-4 opacity-100 transition-opacity duration-300 ease-in-out">
						<div
							ref={scrollContainerRef}
							className="grid grid-cols-5 gap-3 max-h-[510px] overflow-y-auto"
						>
							{sortedMCQs.map((mcq) => {
								// Get the global question number for this MCQ
								const questionNumber = questionNumberMap.get(mcq.id) || 0;
								const isHighlighted = highlightedQuestion === questionNumber;

								return (
									<Link
										to={testTimesUp === false ?  mcq.id : ''}
										smooth={true} duration={500}
										className={`contents ${isHighlighted ? "highlighted-question" : ""}`}
									>
										<span
											className={`text-sm font-bold flex justify-center items-center 
									${isHighlighted ? "text-blue-600 bg-blue-100 rounded-full transition-colors duration-300" : "text-gray-500"}`}
										>
											{questionNumber}
										</span>
										{["A", "B", "C", "D"].map((option, idx) => (
											<div>
											<Button
												disabled={testTimesUp}
												key={`q-${mcq.id}-${option}`}
												className={`flex justify-center items-center p-2 h-10 text-sm font-medium transition-all duration-200 rounded text-black 
										${isHighlighted ? "border-2 border-blue-200" : ""}
										${getButtonClass(mcq.id, idx)}`}
												onClick={() => testTimesUp === false && handleSelect(mcq.id, idx)}
											>
												{option}
											</Button>
											</div>
										))}
									</Link>
								);
							})}
						</div>
						{!liveCheckEnabled && (
							<div className="mt-4 border-t pt-4 transition-all duration-300 ease-in-out">
								<div className="flex justify-between items-center mb-2">
									<span className="text-sm text-gray-500">
										{attemptedCount}/{totalQuestions} answered
									</span>
									<span className="text-sm text-gray-500">
										{Object.keys(answerStatus).length > 0
											? "Results shown"
											: "Not checked"}
									</span>
								</div>
								<Button
									onClick={handleSubmitQuiz}
									loading={isLoading}
									className={`w-full flex items-center justify-center gap-2 transition-all duration-300 ${
										isSubmitTestDisabled()
										? "bg-gray-300 text-gray-500 cursor-not-allowed"
										: "bg-accent hover:bg-green-700 text-white"
									}`}
									disabled={isSubmitTestDisabled()}
								>
									<Check className="w-4 h-4" />
									Submit Test
								</Button>
							</div>
						)}
					</div>
				</CollapsibleContent>
					<MCQResult open={showResultsCard()} setOpen={setShowResults} mcqs={mcqs} selectedAnswers={selectedAnswers} />
			</Collapsible>
		);
	}
);

MCQSolver.displayName = "MCQSolver";

export default MCQSolver;
