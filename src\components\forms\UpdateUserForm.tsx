import { useState, useEffect } from "react";
import { UpdateUserValidation } from "@/features/auth/schema";
import { Control, UseFormSetValue } from "react-hook-form";
import {
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { PhoneInput } from "@/components/ui/phone-input";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from "@/components/ui/command";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronDown, Search, X } from "react-feather";
import {
	cities,
	institutes,
	subjectGroups,
	targetEntryTests,
} from "@/lib/constants/onboardingvalues";
import { cn } from "@/lib/utils";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { User } from "@/features/user/types";

interface UpdateUserFormProps {
	control: Control<UpdateUserValidation>;
	setValue: UseFormSetValue<UpdateUserValidation>;
	userData?: User;
	disableEmail?: boolean;
}

const UpdateUserForm = ({
	control,
	setValue,
	userData,
	disableEmail,
}: UpdateUserFormProps) => {
	const [selectedSubjectGroup, setSelectedSubjectGroup] = useState<Set<string>>(
		() => new Set(userData?.subjectGroup || [])
	);

	const [selectedTests, setSelectedTests] = useState<Set<string>>(
		() => new Set(userData?.targetEntryTests || [])
	);

	// Update subject groups and tests when userData changes
	useEffect(() => {
		if (userData) {
			if (userData.subjectGroup && userData.subjectGroup.length > 0) {
				setSelectedSubjectGroup(new Set(userData.subjectGroup));
				setValue("subjectGroup", userData.subjectGroup);
			}

			if (userData.targetEntryTests && userData.targetEntryTests.length > 0) {
				setSelectedTests(new Set(userData.targetEntryTests));
				setValue("targetEntryTests", userData.targetEntryTests);
			}
		}
	}, [userData, setValue]);

	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<div className="space-y-4">
				{/* Name */}
				<FormField
					control={control}
					name="name"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Full Name
							</FormLabel>
							<FormControl>
								<Input
									className="h-[55px] border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] py-[27px] bg-white"
									placeholder="Your full name"
									{...field}
									value={field.value || ""}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Email */}
				<FormField
					control={control}
					name="email"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Email
							</FormLabel>
							<FormControl>
								<Input
									className="h-[55px] border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] py-[27px]"
									placeholder="Your email address"
									{...field}
									value={field.value || ""}
									disabled={disableEmail}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* City */}
				<FormField
					control={control}
					name="city"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								City
							</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="h-[55px] flex items-center border border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] bg-white">
											<Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
											<span className="flex-grow">
												{field.value
													? cities.find((city) => city.value === field.value)
															?.label
													: "Enter or Search your City"}
											</span>
											<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									side="bottom"
									avoidCollisions={false}
									className="p-0"
								>
									<Command>
										<CommandInput placeholder="Search City..." />
										<CommandList>
											<ScrollArea className="h-72">
												<CommandEmpty>No City Found.</CommandEmpty>
												<CommandGroup>
													{cities.map((city) => (
														<CommandItem
															className="aria-selected:bg-slate-100"
															key={city.value}
															onSelect={() => {
																field.onChange(city.value);
															}}
														>
															<Check
																className={cn(
																	"mr-2 h-4 w-4",
																	city.value === field.value
																		? "opacity-100"
																		: "opacity-0"
																)}
															/>
															{city.label}
														</CommandItem>
													))}
												</CommandGroup>
											</ScrollArea>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Phone Number */}
				<FormField
					control={control}
					name="phoneNumber"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Phone Number
							</FormLabel>
							<FormControl>
								<PhoneInput
									international={true}
									defaultCountry="PK"
									placeholder="Enter a phone number"
									className="h-[55px] shadow-sm rounded-[10px] bg-white"
									{...field}
									value={field.value || ""}
									countrySelectClassName="h-full px-[14px]"
									numberInputClassName="h-full"
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>

			<div className="space-y-4">
				{/* Institute */}
				<FormField
					control={control}
					name="institute"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								School/College
							</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="h-[55px] flex items-center border border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] bg-white">
											<Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
											<span className="flex-grow">
												{field.value
													? institutes.find(
															(inst) => inst.value === field.value
														)?.label
													: "Enter or Search your Institution"}
											</span>
											<ChevronDown className="h-4 w-4 shrink-0 opacity-50" />
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									side="bottom"
									avoidCollisions={false}
									className="p-0"
								>
									<Command>
										<CommandInput placeholder="Search School..." />
										<CommandList>
											<ScrollArea className="h-72">
												<CommandEmpty>No School Found.</CommandEmpty>
												<CommandGroup>
													{institutes.map((inst) => (
														<CommandItem
															className="aria-selected:bg-slate-100"
															key={inst.value}
															onSelect={() => {
																field.onChange(inst.value);
															}}
														>
															<Check
																className={cn(
																	"mr-2 h-4 w-4",
																	inst.value === field.value
																		? "opacity-100"
																		: "opacity-0"
																)}
															/>
															{inst.label}
														</CommandItem>
													))}
												</CommandGroup>
											</ScrollArea>
										</CommandList>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Education Background */}
				<FormField
					control={control}
					name="educationBackground"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Education System
							</FormLabel>
							<Select onValueChange={field.onChange} value={field.value || ""}>
								<FormControl>
									<SelectTrigger className="h-[55px] border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] bg-white">
										<SelectValue placeholder="Select your education system" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="fsc">FSc. (Pakistani)</SelectItem>
									<SelectItem value="o/alevel">
										O/A Level (Cambridge)
									</SelectItem>
									<SelectItem value="other">Other</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Current Class */}
				<FormField
					control={control}
					name="currentClass"
					render={({ field }) => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Year
							</FormLabel>
							<Select onValueChange={field.onChange} value={field.value || ""}>
								<FormControl>
									<SelectTrigger className="h-[55px] border-[#EDF1F3] shadow-sm rounded-[10px] px-[14px] bg-white">
										<SelectValue placeholder="What year are you currently in?" />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value="11">First Year</SelectItem>
									<SelectItem value="12">Second Year</SelectItem>
									<SelectItem value="12">Graduated / Others</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Subject Group */}
				<FormField
					control={control}
					name="subjectGroup"
					render={() => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Education Stream
							</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="h-[55px] border border-[#EDF1F3] shadow-sm rounded-[10px] flex items-center justify-end bg-white">
											<div className="relative mr-auto flex flex-grow flex-wrap items-center overflow-hidden px-3 py-1">
												{selectedSubjectGroup?.size > 0 ? (
													subjectGroups &&
													subjectGroups
														.filter((subjectGroup) =>
															selectedSubjectGroup.has(subjectGroup.value)
														)
														.map((subjectGroup) => (
															<Badge
																key={subjectGroup.value}
																variant="outline"
																className="m-[2px] gap-1 pr-0.5"
															>
																<span>{subjectGroup.label}</span>
																<span
																	onClick={(e) => {
																		e.preventDefault();
																		setSelectedSubjectGroup((prev) => {
																			const next = new Set(prev);
																			next.delete(subjectGroup.value);
																			const filterValues = Array.from(next);
																			setValue("subjectGroup", filterValues);
																			return next;
																		});
																	}}
																	className="flex items-center rounded-sm px-[1px] hover:bg-accent hover:text-red-500"
																>
																	<X size={14} />
																</span>
															</Badge>
														))
												) : (
													<span className="mr-auto text-sm text-muted-foreground">
														Select subject groups...
													</span>
												)}
											</div>
											<div className="flex flex-shrink-0 items-center self-stretch px-1">
												<ChevronDown className="mx-2 h-4 w-4 opacity-50" />
											</div>
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-[var(--radix-popover-trigger-width)] p-0"
									align="start"
								>
									<Command>
										<CommandInput
											placeholder="Search subject groups..."
											className="h-9"
										/>
										<CommandEmpty>No result found.</CommandEmpty>
										<CommandGroup>
											{subjectGroups.map((subjectGroup, index) => {
												const isSelected = selectedSubjectGroup.has(
													subjectGroup.value
												);
												return (
													<CommandItem
														key={index}
														onSelect={() => {
															if (isSelected) {
																selectedSubjectGroup.delete(subjectGroup.value);
															} else {
																selectedSubjectGroup.add(subjectGroup.value);
															}
															const filterValues =
																Array.from(selectedSubjectGroup);
															setValue("subjectGroup", filterValues);
															setSelectedSubjectGroup(new Set(filterValues));
														}}
													>
														<div
															className={cn(
																"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
																isSelected
																	? "bg-primary text-primary-foreground"
																	: "opacity-50 [&_svg]:invisible"
															)}
														>
															<Check className={cn("h-4 w-4")} />
														</div>
														<span>{subjectGroup.label}</span>
													</CommandItem>
												);
											})}
										</CommandGroup>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>

				{/* Target Entry Tests */}
				<FormField
					control={control}
					name="targetEntryTests"
					render={() => (
						<FormItem>
							<FormLabel className="text-[#64748B] font-medium text-sm">
								Target Entry Tests
							</FormLabel>
							<Popover>
								<PopoverTrigger asChild>
									<FormControl>
										<div className="h-[55px] border border-[#EDF1F3] shadow-sm rounded-[10px] flex items-center justify-end bg-white">
											<div className="relative mr-auto flex flex-grow flex-wrap items-center overflow-hidden px-3 py-1">
												{selectedTests?.size > 0 ? (
													targetEntryTests &&
													targetEntryTests
														.filter((test) => selectedTests.has(test.value))
														.map((test) => (
															<Badge
																key={test.value}
																variant="outline"
																className="m-[2px] gap-1 pr-0.5"
															>
																<span>{test.label}</span>
																<span
																	onClick={(e) => {
																		e.preventDefault();
																		setSelectedTests((prev) => {
																			const next = new Set(prev);
																			next.delete(test.value);
																			const filterValues = Array.from(next);
																			setValue(
																				"targetEntryTests",
																				filterValues
																			);
																			return next;
																		});
																	}}
																	className="flex items-center rounded-sm px-[1px] hover:bg-accent hover:text-red-500"
																>
																	<X size={14} />
																</span>
															</Badge>
														))
												) : (
													<span className="mr-auto text-sm text-muted-foreground">
														Select target tests...
													</span>
												)}
											</div>
											<div className="flex flex-shrink-0 items-center self-stretch px-1">
												<ChevronDown className="mx-2 h-4 w-4 opacity-50" />
											</div>
										</div>
									</FormControl>
								</PopoverTrigger>
								<PopoverContent
									className="w-[var(--radix-popover-trigger-width)] p-0"
									align="start"
								>
									<Command>
										<CommandInput
											placeholder="Search entry tests..."
											className="h-9"
										/>
										<CommandEmpty>No result found.</CommandEmpty>
										<CommandGroup>
											{targetEntryTests.map((test, index) => {
												const isSelected = selectedTests.has(test.value);
												return (
													<CommandItem
														key={index}
														onSelect={() => {
															if (isSelected) {
																selectedTests.delete(test.value);
															} else {
																selectedTests.add(test.value);
															}
															const filterValues = Array.from(selectedTests);
															setValue("targetEntryTests", filterValues);
															setSelectedTests(new Set(filterValues));
														}}
													>
														<div
															className={cn(
																"mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary",
																isSelected
																	? "bg-primary text-primary-foreground"
																	: "opacity-50 [&_svg]:invisible"
															)}
														>
															<Check className={cn("h-4 w-4")} />
														</div>
														<span>{test.label}</span>
													</CommandItem>
												);
											})}
										</CommandGroup>
									</Command>
								</PopoverContent>
							</Popover>
							<FormMessage />
						</FormItem>
					)}
				/>
			</div>
		</div>
	);
};

export default UpdateUserForm;
