import { useState, useEffect, useMemo, useRef } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Test } from "@/features/mcqs/types";
import { subjects } from "@/features/learning/constants";
import MCQSolver, {
	MCQSolverHandle,
} from "@/components/layout/mcqs/mcq-solver";
import React, { Suspense } from "react";
import { MCQPlaceholder } from "./mcq-placeholder";


const MCQCard = React.lazy(() => import("./mcq-card"));

// Define a type for the subject IDs based on the subjects constant
type SubjectId = (typeof subjects)[number]["id"];

type SubjectTabsProps = {
	test: Test;
	liveCheckEnabled: boolean;
	selectedAnswers: Record<string, number>;
	answerStatus: Record<string, boolean>;
	showResults: boolean;
	onAnswerSelect: (questionId: string, optionIndex: number) => void;
	onCheckAnswers: () => void;
	testTimesUp: boolean;
	setShowResults: (enabled: boolean) => void;
};

const SubjectTabs = ({
	test,
	liveCheckEnabled,
	selectedAnswers,
	answerStatus,
	showResults,
	onAnswerSelect,
	onCheckAnswers,
	testTimesUp,
	setShowResults,
}: SubjectTabsProps) => {
	// Add ref for MCQSolver
	const solverRef = useRef<MCQSolverHandle>(null);

	// Get all subjects that have MCQs in the test
	const availableSubjects = useMemo(() => {
		// Get unique subjects from MCQs
		const uniqueSubjects = [...new Set(test.mcqs.map((mcq) => mcq.subject))];

		// Sort according to the order in the global subjects constant
		// This ensures tabs are displayed in the same order as defined in subjects
		return subjects
			.filter((subject) => uniqueSubjects.includes(subject.id))
			.map((subject) => subject.id);
	}, [test.mcqs]);

	const defaultSubject =
		availableSubjects.length > 0 ? availableSubjects[0] : subjects[0].id;

	// State to track the currently selected subject
	const [selectedSubject, setSelectedSubject] = useState<SubjectId>(
		defaultSubject as SubjectId
	);
	// const [visibleMCQs, setVisibleMCQs] = useState<string[]>([]);

	// Create the ref at component level, not inside useEffect
	// const visibleMCQsRef = useRef<string[]>([]);

	// Filter MCQs based on selected subject
	const filteredMCQs = test.mcqs.filter(
		(mcq) => mcq.subject === selectedSubject
	);

	// Group MCQs by subject based on the tab order
	const mcqsBySubject = useMemo(() => {
		const grouped: Record<string, typeof test.mcqs> = {};

		// Group MCQs by subject in the tab display order
		availableSubjects.forEach((subject) => {
			grouped[subject] = test.mcqs.filter((mcq) => mcq.subject === subject);
		});

		return grouped;
	}, [test.mcqs, availableSubjects]);

	// Create a mapping of question IDs to their sequential numbers based on tab order
	const questionNumberMap = useMemo(() => {
		const map = new Map<string, number>();
		let counter = 1;

		// For each subject in the tab order
		availableSubjects.forEach((subject) => {
			// Get questions for this subject
			const subjectMCQs = mcqsBySubject[subject] || [];

			// Assign sequential numbers
			subjectMCQs.forEach((mcq) => {
				map.set(mcq.id, counter++);
			});
		});

		return map;
	}, [mcqsBySubject, availableSubjects]);

	// Update visible MCQs when selected subject changes
	// useEffect(() => {
	// 	// When subject changes, show initial set of MCQs for that subject
	// 	const initialVisible = filteredMCQs.slice(0, 10).map((mcq) => mcq.id);
	// 	setVisibleMCQs(initialVisible);

	// 	// Update the ref with current visible MCQs
	// 	visibleMCQsRef.current = initialVisible;

	// 	// Create an intersection observer to lazy load more MCQs as user scrolls
	// 	const observer = new IntersectionObserver(
	// 		(entries) => {
	// 			const newVisibleMCQs: string[] = [];

	// 			entries.forEach((entry) => {
	// 				if (entry.isIntersecting) {
	// 					const id = entry.target.getAttribute("data-mcq-id");
	// 					if (id && !visibleMCQsRef.current.includes(id)) {
	// 						newVisibleMCQs.push(id);
	// 					}
	// 				}
	// 			});

	// 			// Only update state if we have new MCQs to show
	// 			if (newVisibleMCQs.length > 0) {
	// 				setVisibleMCQs((prev) => {
	// 					const updated = [...prev, ...newVisibleMCQs];
	// 					visibleMCQsRef.current = updated;
	// 					return updated;
	// 				});
	// 			}
	// 		},
	// 		{ rootMargin: "200px 0px" }
	// 	);

	// 	// Observe MCQ containers for lazy loading
	// 	document.querySelectorAll(".mcq-container").forEach((el) => {
	// 		const id = el.getAttribute("data-mcq-id");
	// 		if (id) {
	// 			observer.observe(el);
	// 		}
	// 	});

	// 	return () => {
	// 		observer.disconnect();
	// 	};
	// }, [selectedSubject, filteredMCQs]); // Remove visibleMCQs from dependencies

	const handleTabClick = (value: string) => {
		// Cast the string value to SubjectId since we know it's valid in this context
		const subjectId = value as SubjectId;
		setSelectedSubject(subjectId);
		// Functionality to highlight the starting point has been removed
	};

	  const [isVisible, setIsVisible] = useState(false);

  	useEffect(() => {
      const toggleVisibility = () => {
      setIsVisible(window.scrollY > 200);
    };

    window.addEventListener('scroll', toggleVisibility);
    	return () => window.removeEventListener('scroll', toggleVisibility);
  	}, []);

  	const scrollToTop = () => {
    	window.scrollTo({ top: 0, behavior: 'smooth' });
  	};

	return (
		<>
			<Tabs
				value={selectedSubject}
				onValueChange={handleTabClick}
				className="w-full"
			>
				<TabsList className="flex">
					{subjects
						.filter((subject) => availableSubjects.includes(subject.id))
						.map((subject) => (
							<TabsTrigger
								key={subject.id}
								value={subject.id}
								className="rounded-t-[10px] px-6 font-medium text-[13px] data-[state=active]:text-gray-700"
							>
								{subject.label}
							</TabsTrigger>
						))}
				</TabsList>
			</Tabs>

			<div className="bg-white rounded-lg p-6 min-h-screen flex gap-6">
				<div className="rounded-3xl border-2 space-y-8 flex-1">
					{filteredMCQs.length > 0 ? (
						filteredMCQs.map((mcq) => (
							<div key={mcq.id} data-mcq-id={mcq.id} className="mcq-container">
									<Suspense fallback={<MCQPlaceholder />}>
										<div>
										<MCQCard
											mcq={mcq}
											selectedAnswer={selectedAnswers[mcq.id]}
											isCorrect={answerStatus[mcq.id]}
											liveCheckEnabled={liveCheckEnabled}
											showResults={showResults}
											questionNumber={questionNumberMap.get(mcq.id)} // Use ordered question number
										/>
										</div>
									</Suspense>
							</div>
						))
					) : (
						<div className="p-6 text-center">
							<p className="text-gray-500">
								No questions available for this subject.
							</p>
						</div>
					)}
				</div>
				<div className="h-full sticky top-0">
					<MCQSolver
						quizId={test.id}
						ref={solverRef}
						mcqs={test.mcqs}
						onAnswerSelect={onAnswerSelect}
						selectedAnswers={selectedAnswers}
						answerStatus={answerStatus}
						liveCheckEnabled={liveCheckEnabled}
						onCheckAnswers={onCheckAnswers}
						questionNumberMap={questionNumberMap} // Pass the question number map to the solver
						testTimesUp={testTimesUp}
						showResults={showResults}
						setShowResults={setShowResults}
					/>
				</div>
				<button
      				onClick={scrollToTop}
					className={`fixed bottom-8 left-1/2 z-50 py-3 px-6 rounded-full flex items-center gap-2 bg-gradient-to-br from-indigo-500 to-purple-500 text-white shadow-lg transition-opacity ${
						isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'
					}`}
					aria-label="Scroll to top"
					>
					Scroll to top
					<svg width="25" height="25" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M7.14645 2.14645C7.34171 1.95118 7.65829 1.95118 7.85355 2.14645L11.8536 6.14645C12.0488 6.34171 12.0488 6.65829 11.8536 6.85355C11.6583 7.04882 11.3417 7.04882 11.1464 6.85355L8 3.70711L8 12.5C8 12.7761 7.77614 13 7.5 13C7.22386 13 7 12.7761 7 12.5L7 3.70711L3.85355 6.85355C3.65829 7.04882 3.34171 7.04882 3.14645 6.85355C2.95118 6.65829 2.95118 6.34171 3.14645 6.14645L7.14645 2.14645Z" fill="currentColor" fillRule="evenodd" clipRule="evenodd"></path></svg>
			</button>
			</div>
		</>
	);
};

export default SubjectTabs;
